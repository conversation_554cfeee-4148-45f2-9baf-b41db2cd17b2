import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glass_container.dart';

class AIAssistant {
  final String name;
  final String category;
  final double rating;
  final Color color;
  final String description;
  final String price;
  final String users;

  const AIAssistant({
    required this.name,
    required this.category,
    required this.rating,
    required this.color,
    required this.description,
    required this.price,
    required this.users,
  });
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _userName = '';

  final List<AIAssistant> _aiAssistants = [
    AIAssistant(
      name: 'Parenting Helper',
      category: 'Helper',
      rating: 4.4,
      color: AppColors.textSecondary,
      description:
          'Get expert advice on child development and parenting strategies.',
      price: '\$2.99',
      users: '+500k',
    ),
    AIAssistant(
      name: 'Game Strategist',
      category: 'Strategist',
      rating: 4.5,
      color: AppColors.accentPink,
      description:
          'Master your favorite games with advanced strategies and tips.',
      price: '\$3.99',
      users: '+750k',
    ),
    AIAssistant(
      name: 'Travel Genie',
      category: 'Genie',
      rating: 4.7,
      color: AppColors.accentBlue,
      description: 'Plan perfect trips with personalized recommendations.',
      price: '\$4.99',
      users: '+1M',
    ),
    AIAssistant(
      name: 'Fitness Buddy',
      category: 'Buddy',
      rating: 4.9,
      color: AppColors.accentGreen,
      description: 'Your personal trainer for workouts and nutrition guidance.',
      price: '\$5.99',
      users: '+2M',
    ),
    AIAssistant(
      name: 'Love Advisor',
      category: 'Advisor',
      rating: 4.9,
      color: AppColors.accentYellow,
      description: 'Relationship guidance and dating advice from experts.',
      price: '\$3.99',
      users: '+800k',
    ),
    AIAssistant(
      name: 'Health Guardian',
      category: 'Guardian',
      rating: 4.3,
      color: AppColors.accentPink,
      description: 'Monitor your health and get medical insights.',
      price: '\$6.99',
      users: '+600k',
    ),
    AIAssistant(
      name: 'Study Mentor',
      category: 'Mentor',
      rating: 4.1,
      color: AppColors.textSecondary,
      description:
          'Simplify your studies with homework help, practice quizzes, and subject-specific insights.',
      price: '\$3.99',
      users: '+750k',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadUserName();
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xl),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome header
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back,',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  )
                      .animate()
                      .slideX(
                        begin: -0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(),
                  Text(
                    _userName,
                    style: AppTextStyles.h1.copyWith(
                      fontSize: 32,
                      fontWeight: FontWeight.w800,
                    ),
                  )
                      .animate()
                      .slideX(
                        begin: -0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 200.ms),
                ],
              ),

              const SizedBox(height: AppSpacing.xl),

              // Explore section
              Text(
                'Explore Intelligent',
                style: AppTextStyles.h2.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              )
                  .animate()
                  .slideX(
                    begin: -0.3,
                    duration: 600.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(delay: 400.ms),

              Text(
                'Chatbots',
                style: AppTextStyles.h2.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.textSecondary,
                ),
              )
                  .animate()
                  .slideX(
                    begin: -0.3,
                    duration: 600.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(delay: 500.ms),

              const SizedBox(height: AppSpacing.xl),

              // AI Assistants Stack
              SizedBox(
                height: 200,
                child: Stack(
                  children: [
                    // Build cards in reverse order so the first card appears on top
                    for (int i = _aiAssistants.length - 1; i >= 0; i--)
                      _buildStackedCard(_aiAssistants[i], i),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStackedCard(AIAssistant assistant, int index) {
    // Calculate the vertical offset for stacking effect
    double topOffset = index * 50.0; // Each card is offset by 50 pixels
    double scale = 1.0 - (index * 0.02); // Slight scale reduction for depth
    bool isTopCard = index == 0;
    bool isBottomCard = index == _aiAssistants.length - 1;

    return Positioned(
      top: topOffset,
      left: index * 8.0, // Slight horizontal offset
      right: index * 8.0,
      child: Transform.scale(
        scale: scale,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.xl),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                assistant.color.withValues(alpha: 0.3),
                assistant.color.withValues(alpha: 0.1),
              ],
            ),
            border: Border.all(
              color: AppColors.glassBorder.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: GlassCard(
            borderRadius: AppBorderRadius.xl,
            padding: const EdgeInsets.all(AppSpacing.lg),
            onTap: () {
              // Handle card tap
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with name and rating
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            assistant.name.split(' ')[0],
                            style: AppTextStyles.h3.copyWith(
                              fontWeight: FontWeight.w700,
                              color: isTopCard
                                  ? AppColors.textPrimary
                                  : AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            assistant.category,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: isTopCard
                                  ? AppColors.textSecondary
                                  : AppColors.textMuted,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xs,
                      ),
                      decoration: BoxDecoration(
                        color: assistant.color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            assistant.rating.toString(),
                            style: AppTextStyles.bodySmall.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Icon(
                            Icons.star,
                            size: 14,
                            color: assistant.color,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Show detailed content only for the bottom card (Study Mentor)
                if (isBottomCard) ...[
                  const SizedBox(height: AppSpacing.md),
                  Text(
                    assistant.description,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.md),
                  Row(
                    children: [
                      _buildBadge('BD'),
                      const SizedBox(width: AppSpacing.xs),
                      _buildBadge('PC'),
                      const SizedBox(width: AppSpacing.xs),
                      _buildBadge('UH'),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        assistant.users,
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.lg),
                  Row(
                    children: [
                      Text(
                        assistant.price,
                        style: AppTextStyles.h2.copyWith(
                          fontWeight: FontWeight.w800,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        '/month',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    )
        .animate()
        .slideY(
          begin: 0.5,
          duration: (800 + (index * 150)).ms,
          curve: Curves.easeOutBack,
        )
        .fadeIn(delay: (200 + (index * 100)).ms)
        .scale(
          begin: const Offset(0.8, 0.8),
          duration: (800 + (index * 150)).ms,
          curve: Curves.easeOutBack,
        );
  }
}
