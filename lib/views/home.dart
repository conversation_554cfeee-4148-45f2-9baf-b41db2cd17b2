import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glass_container.dart';

class AIAssistant {
  final String name;
  final String category;
  final double rating;
  final Color color;
  final String description;
  final String price;
  final String users;

  const AIAssistant({
    required this.name,
    required this.category,
    required this.rating,
    required this.color,
    required this.description,
    required this.price,
    required this.users,
  });
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _userName = '';

  final List<AIAssistant> _aiAssistants = [
    AIAssistant(
      name: 'Parenting Helper',
      category: 'Helper',
      rating: 4.4,
      color: AppColors.textSecondary,
      description:
          'Get expert advice on child development and parenting strategies.',
      price: '\$2.99',
      users: '+500k',
    ),
    AIAssistant(
      name: 'Game Strategist',
      category: 'Strategist',
      rating: 4.5,
      color: AppColors.accentPink,
      description:
          'Master your favorite games with advanced strategies and tips.',
      price: '\$3.99',
      users: '+750k',
    ),
    AIAssistant(
      name: 'Travel Genie',
      category: 'Genie',
      rating: 4.7,
      color: AppColors.accentBlue,
      description: 'Plan perfect trips with personalized recommendations.',
      price: '\$4.99',
      users: '+1M',
    ),
    AIAssistant(
      name: 'Fitness Buddy',
      category: 'Buddy',
      rating: 4.9,
      color: AppColors.accentGreen,
      description: 'Your personal trainer for workouts and nutrition guidance.',
      price: '\$5.99',
      users: '+2M',
    ),
    AIAssistant(
      name: 'Love Advisor',
      category: 'Advisor',
      rating: 4.9,
      color: AppColors.accentYellow,
      description: 'Relationship guidance and dating advice from experts.',
      price: '\$3.99',
      users: '+800k',
    ),
    AIAssistant(
      name: 'Health Guardian',
      category: 'Guardian',
      rating: 4.3,
      color: AppColors.accentPink,
      description: 'Monitor your health and get medical insights.',
      price: '\$6.99',
      users: '+600k',
    ),
    AIAssistant(
      name: 'Study Mentor',
      category: 'Mentor',
      rating: 4.1,
      color: AppColors.textSecondary,
      description:
          'Simplify your studies with homework help, practice quizzes, and subject-specific insights.',
      price: '\$3.99',
      users: '+750k',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadUserName();
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xl),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome header
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back,',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  )
                      .animate()
                      .slideX(
                        begin: -0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(),
                  Text(
                    _userName,
                    style: AppTextStyles.h1.copyWith(
                      fontSize: 32,
                      fontWeight: FontWeight.w800,
                    ),
                  )
                      .animate()
                      .slideX(
                        begin: -0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 200.ms),
                ],
              ),

              const SizedBox(height: AppSpacing.xl),

              // Explore section
              Text(
                'Explore Intelligent',
                style: AppTextStyles.h2.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              )
                  .animate()
                  .slideX(
                    begin: -0.3,
                    duration: 600.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(delay: 400.ms),

              Text(
                'Chatbots',
                style: AppTextStyles.h2.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.textSecondary,
                ),
              )
                  .animate()
                  .slideX(
                    begin: -0.3,
                    duration: 600.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(delay: 500.ms),

              const SizedBox(height: AppSpacing.xl),

              // AI Assistants Stack
              SizedBox(
                height: 200,
                child: ListView.builder(
                  itemCount: _aiAssistants.length,
                  itemBuilder: (context, index) {
                    final assistant = _aiAssistants[index];
                    return _buildAssistantCard(assistant, index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAssistantCard(AIAssistant assistant, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with name and rating
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      assistant.name.split(' ')[0],
                      style: AppTextStyles.h3.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      assistant.category,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: assistant.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      assistant.rating.toString(),
                      style: AppTextStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 2),
                    Icon(
                      Icons.star,
                      size: 14,
                      color: assistant.color,
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (index == _aiAssistants.length - 1) ...[
            const SizedBox(height: AppSpacing.md),
            Text(
              assistant.description,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                height: 1.4,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceElevated,
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Text(
                    'BD',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.xs),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceElevated,
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Text(
                    'PC',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.xs),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceElevated,
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Text(
                    'UH',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  assistant.users,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.lg),
            Row(
              children: [
                Text(
                  assistant.price,
                  style: AppTextStyles.h2.copyWith(
                    fontWeight: FontWeight.w800,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  '/month',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    )
        .animate()
        .slideY(
          begin: 0.3,
          duration: (600 + (index * 100)).ms,
          curve: Curves.easeOut,
        )
        .fadeIn(delay: (400 + (index * 100)).ms);
  }
}
